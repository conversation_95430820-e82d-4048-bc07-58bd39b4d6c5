<div class="category-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <div class="mapping-layout">
      <!-- Left Panel: Category Selection -->
      <div class="left-panel">
        <div class="panel-header">
          <mat-icon>category</mat-icon>
          <span>Select Categories</span>
        </div>

        <mat-form-field appearance="outline" class="category-select-field">
          <mat-select
            [value]="selectedCategoriesForMapping"
            (selectionChange)="onCategoriesSelectionChange($event.value)"
            multiple
            placeholder="Select categories ({{ selectedCategoriesForMapping.length }}/{{ availableCategoriesForMapping.length }})"
          >
            <mat-option>
              <ngx-mat-select-search [formControl]="categoryFilterCtrl" placeholderLabel="Search categories..."
                noEntriesFoundLabel="No categories found">
              </ngx-mat-select-search>
            </mat-option>
            <!-- Select All / Deselect All Options -->
            <div class="select-all-custom-option" (click)="toggleAllCategories($event)">
              <strong>{{areAllCategoriesSelected() ? 'Deselect All' : 'Select All'}}</strong>
            </div>
            <mat-divider></mat-divider>
            <mat-option
              *ngFor="let category of filteredCategoriesForMapping; trackBy: trackByCategory"
              [value]="category"
            >
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Right Panel: Work Area Configuration -->
      <div class="right-panel">
        <div class="panel-header">
          <mat-icon>work</mat-icon>
          <span>Configure Work Area Mappings</span>
        </div>

        <div class="workarea-content" *ngIf="selectedCategoriesForMapping.length > 0; else selectCategoryPrompt">
          <!-- Multiple Categories Configuration -->
          <div class="selected-categories-info">
            <strong>Selected Categories ({{ selectedCategoriesForMapping.length }})</strong>
            <div class="category-chips">
              <mat-chip-listbox>
                <mat-chip *ngFor="let category of selectedCategoriesForMapping">
                  {{ category }}
                </mat-chip>
              </mat-chip-listbox>
            </div>
          </div>

          <!-- Work Area Selection for All Selected Categories -->
          <div class="bulk-workarea-section">
            <div class="section-label">
              <mat-icon>work</mat-icon>
              <span>Assign Work Areas to Selected Categories</span>
            </div>

            <mat-form-field appearance="outline" class="workarea-field">
              <mat-select
                [value]="getCommonWorkAreas()"
                (selectionChange)="onBulkWorkAreasSelectionChange($event.value)"
                multiple
                placeholder="Select work areas for all selected categories"
              >
                <mat-option>
                  <ngx-mat-select-search [formControl]="workAreaFilterCtrl" placeholderLabel="Search work areas..."
                    noEntriesFoundLabel="No work areas found">
                  </ngx-mat-select-search>
                </mat-option>
                <!-- Select All / Deselect All Options -->
                <div class="select-all-custom-option" (click)="toggleAllWorkAreas($event)">
                  <strong>{{areAllWorkAreasSelected() ? 'Deselect All' : 'Select All'}}</strong>
                </div>
                <mat-divider></mat-divider>
                <mat-option
                  *ngFor="let workArea of filteredAvailableWorkAreas; trackBy: trackByWorkArea"
                  [value]="workArea"
                >
                  {{ workArea }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Current Mappings Summary -->
          <div class="mappings-summary" *ngIf="mappings.length > 0">
            <h4>Configured Categories ({{ mappings.length }})</h4>
            <div class="mapping-chips">
              <mat-chip-listbox>
                <mat-chip
                  *ngFor="let mapping of mappings"
                  [class.selected-chip]="selectedCategoriesForMapping.includes(mapping.categoryName)"
                >
                  {{ mapping.categoryName }} ({{ mapping.workAreas.length }})
                  <button
                    matChipRemove
                    (click)="removeCategoryMapping(mapping.categoryName); $event.stopPropagation()"
                  >
                    <mat-icon>cancel</mat-icon>
                  </button>
                </mat-chip>
              </mat-chip-listbox>
            </div>
          </div>
        </div>

        <ng-template #selectCategoryPrompt>
          <div class="empty-state">
            <mat-icon class="empty-icon">arrow_back</mat-icon>
            <p>Select categories from the left to configure work area mappings</p>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button
      mat-button
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>

    <button
      mat-raised-button
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>
