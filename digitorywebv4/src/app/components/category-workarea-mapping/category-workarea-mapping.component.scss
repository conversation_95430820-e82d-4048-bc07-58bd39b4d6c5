// ===== MATERIAL DESIGN THEME OVERRIDES =====
::ng-deep {
  // Form fields
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px;
      min-height: 36px;
    }

    .mat-mdc-form-field-infix {
      padding: 6px 12px;
      min-height: 24px;
      border-top: none;
    }

    .mat-mdc-form-field-flex {
      align-items: center;
      height: 36px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-outline {
      color: #dee2e6;
    }

    .mat-mdc-form-field-outline-thick {
      color: #ffb366;
    }

    .mat-mdc-form-field-label {
      color: #666;
      font-size: 13px;
      top: 18px;
    }

    &.mat-focused .mat-mdc-form-field-label {
      color: #ffb366;
    }

    &.mat-form-field-should-float .mat-mdc-form-field-label {
      transform: translateY(-12px) scale(0.75);
    }
  }

  // Select dropdowns
  .mat-mdc-select {
    .mat-mdc-select-trigger {
      height: 36px;
      display: flex;
      align-items: center;
    }

    .mat-mdc-select-value {
      font-size: 13px;
      line-height: 24px;
    }

    .mat-mdc-select-arrow {
      color: #ffb366;
    }
  }

  // Select panel
  .mat-mdc-select-panel .mat-mdc-option {
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    padding: 0 16px;

    &.mat-mdc-option-active {
      background: rgba(255, 179, 102, 0.1);
      color: #ffb366;
    }

    &:hover {
      background: rgba(255, 179, 102, 0.05);
    }
  }

  // Input elements
  .mat-mdc-input-element {
    font-size: 13px;
    height: 24px;
    line-height: 24px;
  }

  // Buttons
  .mat-mdc-raised-button,
  .mat-mdc-outlined-button {
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    font-size: 13px;

    &.mat-primary {
      background-color: #ffb366;
      color: white;

      &:hover {
        background-color: #ffa64d;
      }
    }
  }

  .mat-mdc-outlined-button.mat-primary {
    border-color: #ffb366;
    color: #ffb366;
    background-color: transparent;

    &:hover {
      background-color: rgba(255, 179, 102, 0.05);
    }
  }
}

.category-mapping-container {
  padding: 0;
  max-width: none;
  margin: 0;
  background: transparent;
  min-height: 300px;
  height: 100%;
  overflow: visible;
  display: flex;
  flex-direction: column;

  &.dialog-mode {
    padding: 16px;
    max-width: 850px;
    overflow: visible;
  }
}

// Loading State
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex: 1;
}

// Main Content
.mapping-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible;
}

// Validation Errors
.validation-errors {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 20px;

  .error-icon {
    color: #856404;
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-top: 2px;
  }

  .error-list {
    flex: 1;

    .error-message {
      margin: 0 0 4px 0;
      color: #856404;
      font-size: 14px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Empty State
.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;

  .empty-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
  }
}

// Form Styles
.mapping-form {
  flex: 1;
  overflow: visible;

  .mappings-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: visible;
  }
}

// Mapping Row Styles
.mapping-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #ffb366;
    box-shadow: 0 1px 4px rgba(255, 179, 102, 0.1);
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 200px;
    font-weight: 500;
    color: #333;

    .category-icon {
      color: #ffb366;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    span {
      font-size: 14px;
    }
  }

  .workareas-dropdown {
    flex: 1;
    margin-left: 16px;

    .workareas-field {
      width: 100%;
      margin: 0;
    }
  }
}

// Summary Section
.mapping-summary {
  margin-top: 32px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .summary-grid {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .summary-item {
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;

    .summary-category {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .summary-workareas {
      .workarea-count {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      .workarea-list {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// Actions Section
.mapping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0;
  margin-top: 20px;
  border-top: 1px solid #e9ecef;

  .cancel-button {
    color: #666;
    border-color: #dee2e6;

    &:hover {
      background-color: #f8f9fa;
      border-color: #adb5bd;
    }
  }

  .save-button {
    position: relative;
    min-width: 120px;

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }

    &:disabled {
      background-color: #e9ecef;
      color: #6c757d;
      cursor: not-allowed;
    }
  }
}

  .right-panel {
    flex: 1;
    min-width: 0;
  }

  .panel-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
    font-size: 14px;

    mat-icon {
      color: #ff9800;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }

  // Left panel styles
  .category-select-field {
    margin: 16px;
    width: calc(100% - 32px);
  }

  // Right panel styles
  .workarea-content {
    padding: 16px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .selected-category-info {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
  }

  .workarea-field {
    width: 100%;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
    text-align: center;

    .empty-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // Mappings summary in right panel
  .mappings-summary {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .mapping-chips {
      .mat-mdc-chip-listbox {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .mat-mdc-chip {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(255, 152, 0, 0.1);
          }

          &.selected-chip {
            background-color: rgba(255, 152, 0, 0.2);
            border-color: #ff9800;
          }

          .mat-mdc-chip-remove {
            margin-left: 4px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }

  .validation-errors {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
    background-color: #ffebee;
    border: 1px solid #f44336;
    border-radius: 4px;
    margin-bottom: 16px;

    .error-icon {
      color: #f44336;
      margin-top: 2px;
    }

    .error-list {
      flex: 1;

      .error-message {
        margin: 0;
        color: #d32f2f;
        font-size: 14px;
        line-height: 1.4;

        &:not(:last-child) {
          margin-bottom: 4px;
        }
      }
    }
  }

  .mapping-form {
    flex: 1;

    .mappings-container {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .empty-state-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        color: #666;
        background-color: white;
        border-radius: 8px;
        border: 2px dashed #ddd;

        .empty-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          margin-bottom: 16px;
          color: #999;
        }

        p {
          margin: 0;
          font-size: 16px;
          line-height: 1.5;
        }
      }

      .mapping-row {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #ff9800;

        .category-name {
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 200px;
          font-weight: 500;
          color: #333;

          .category-icon {
            color: #ff9800;
            font-size: 20px;
            width: 20px;
            height: 20px;
          }

          span {
            font-size: 14px;
          }
        }

        .workareas-dropdown {
          flex: 1;

          .workareas-field {
            width: 100%;
          }
        }
      }
    }
  }

  .mapping-summary {
    margin-top: 24px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 18px;
      font-weight: 500;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .summary-item {
        padding: 12px;
        background-color: #f5f5f5;
        border-radius: 6px;
        border-left: 3px solid #ff9800;

        .summary-category {
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .summary-workareas {
          .workarea-count {
            display: inline-block;
            background-color: #ff9800;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .workarea-list {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .mapping-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;

    .cancel-button {
      color: #666;
    }

    .save-button {
      background-color: #ff9800;
      color: white;

      &:disabled {
        background-color: #ccc;
        color: #999;
      }

      .button-spinner {
        margin-right: 8px;
      }

      mat-icon {
        margin-right: 8px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .category-mapping-container {
    padding: 12px;

    .mapping-form .mappings-container .mapping-row {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .category-name {
        min-width: auto;
        justify-content: center;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }

    .mapping-summary .summary-grid {
      grid-template-columns: 1fr;
    }

    .mapping-actions {
      flex-direction: column-reverse;

      button {
        width: 100%;
      }
    }
  }
}
